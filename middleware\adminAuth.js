const db = require('../config/database');const { asyncHandler } = require('./errorHandler');const ROLE_HIERARCHY = {'Super Admin': 4,'Data Center Admin': 3,'Librarian': 2,'Librarian Staff': 1};const verifyAdmin = asyncHandler(async (req, res, next) => {const { adminID } = req.body || req.params || req.query;if (!adminID) {return res.status(401).json({success: false,error: '❌ Admin ID required for authentication'});}const selectQuery = `SELECT AdminID, FullName, Email, Role, Status FROM admins WHERE AdminID = ?`;const [results] = await db.execute(selectQuery, [adminID]);if (results.length === 0) {return res.status(401).json({success: false,error: '❌ Admin not found'});}const admin = results[0];if (admin.Status !== 'Active') {return res.status(401).json({success: false,error: '❌ Admin account is inactive'});}req.admin = admin;next();});const requireRole = (requiredRole) => {return asyncHandler(async (req, res, next) => {if (!req.admin) {return res.status(401).json({success: false,error: '❌ Admin authentication required'});}const adminRoleLevel = ROLE_HIERARCHY[req.admin.Role];const requiredRoleLevel = ROLE_HIERARCHY[requiredRole];if (adminRoleLevel < requiredRoleLevel) {return res.status(403).json({success: false,error: `❌ Access denied. Required role: ${requiredRole} or higher. Current role: ${req.admin.Role}`});}next();});};const canManageAdmin = asyncHandler(async (req, res, next) => {if (!req.admin) {return res.status(401).json({success: false,error: '❌ Admin authentication required'});}const { adminID: targetAdminID } = req.params;if (!targetAdminID) {return next();}const selectQuery = `SELECT Role FROM admins WHERE AdminID = ?`;const [results] = await db.execute(selectQuery, [targetAdminID]);if (results.length === 0) {return res.status(404).json({success: false,error: '❌ Target admin not found'});}const targetAdmin = results[0];const currentAdminRoleLevel = ROLE_HIERARCHY[req.admin.Role];const targetAdminRoleLevel = ROLE_HIERARCHY[targetAdmin.Role];if (req.admin.Role === 'Super Admin') {return next();}if (currentAdminRoleLevel <= targetAdminRoleLevel) {return res.status(403).json({success: false,error: `❌ Cannot manage admin with role: ${targetAdmin.Role}. Insufficient permissions.`});}next();});const requireSuperAdmin = requireRole('Super Admin');const requireDataCenterAdmin = requireRole('Data Center Admin');const requireLibrarian = requireRole('Librarian');const logAdminAction = (action, affectedTable = null) => {return asyncHandler(async (req, res, next) => {if (!req.admin) {return next();}try {const affectedID = req.params.id || req.params.adminID || req.params.studentID || req.params.bookID || null;const logQuery = `INSERT INTO adminauditlogs (AdminID, Action, AffectedTable, AffectedID) VALUES (?, ?, ?, ?)`;await db.execute(logQuery, [req.admin.AdminID, action, affectedTable, affectedID]);} catch (error) {console.error('Failed to log admin action:', error);}next();});};

const PERMISSIONS = {CREATE_ADMIN: ['Super Admin'],UPDATE_ADMIN: ['Super Admin', 'Data Center Admin'],DELETE_ADMIN: ['Super Admin'],VIEW_ADMIN: ['Super Admin', 'Data Center Admin', 'Librarian'],CREATE_STUDENT: ['Super Admin', 'Data Center Admin', 'Librarian'],UPDATE_STUDENT: ['Super Admin', 'Data Center Admin', 'Librarian'],DELETE_STUDENT: ['Super Admin', 'Data Center Admin'],VIEW_STUDENT: ['Super Admin', 'Data Center Admin', 'Librarian', 'Librarian Staff'],CREATE_BOOK: ['Super Admin', 'Data Center Admin', 'Librarian'],UPDATE_BOOK: ['Super Admin', 'Data Center Admin', 'Librarian'],DELETE_BOOK: ['Super Admin', 'Data Center Admin'],VIEW_BOOK: ['Super Admin', 'Data Center Admin', 'Librarian', 'Librarian Staff'],CREATE_TRANSACTION: ['Super Admin', 'Data Center Admin', 'Librarian', 'Librarian Staff'],UPDATE_TRANSACTION: ['Super Admin', 'Data Center Admin', 'Librarian'],VIEW_TRANSACTION: ['Super Admin', 'Data Center Admin', 'Librarian', 'Librarian Staff'],VIEW_REPORTS: ['Super Admin', 'Data Center Admin', 'Librarian'],VIEW_AUDIT_LOGS: ['Super Admin', 'Data Center Admin'],VIEW_SYSTEM_LOGS: ['Super Admin', 'Data Center Admin']};const requirePermission = (permission) => {return asyncHandler(async (req, res, next) => {if (!req.admin) {return res.status(401).json({success: false,error: '❌ Admin authentication required'});}const allowedRoles = PERMISSIONS[permission];if (!allowedRoles || !allowedRoles.includes(req.admin.Role)) {return res.status(403).json({success: false,error: `❌ Access denied. Permission '${permission}' required. Current role: ${req.admin.Role}`});}next();});};module.exports = {verifyAdmin,requireRole,requireSuperAdmin,requireDataCenterAdmin,requireLibrarian,canManageAdmin,logAdminAction,requirePermission,PERMISSIONS,ROLE_HIERARCHY};
