const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const db = require('../config/database');
const { validateStudentRegistration, validateStudentUpdate, validateStudentId } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');

// JWT secret key (should be in environment variables)
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-this-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

// Generate JWT token for student
const generateStudentToken = (student) => {
  return jwt.sign(
    {
      studentId: student.StudentID,
      fullName: student.FullName,
      email: student.Email,
      type: 'student'
    },
    JWT_SECRET,
    { expiresIn: JWT_EXPIRES_IN }
  );
};

// Validation middleware for student login
const validateStudentLogin = (req, res, next) => {
  const { studentId, password } = req.body;

  if (!studentId || !password) {
    return res.status(400).json({
      success: false,
      error: '❌ Student ID and password are required'
    });
  }

  // Validate student ID format (YYYY-NNNNN)
  const studentIdPattern = /^[0-9]{4}-[0-9]{5}$/;
  if (!studentIdPattern.test(studentId)) {
    return res.status(400).json({
      success: false,
      error: '❌ Invalid student ID format. Expected format: YYYY-NNNNN'
    });
  }

  next();
};

// POST /register-student
router.post('/register-student', validateStudentRegistration, asyncHandler(async (req, res) => {
  let {
    studentID,
    fullName,
    course,
    yearLevel,
    section,
    email,
    phoneNumber,
    password
  } = req.body;

  // Set defaults for optional fields
  course = course?.trim() !== '' ? course : 'N/A';
  section = section?.trim() !== '' ? section : 'N/A';
  phoneNumber = phoneNumber?.trim() !== '' ? phoneNumber : 'N/A';
  yearLevel = yearLevel && !isNaN(yearLevel) ? parseInt(yearLevel) : 0;

  // Hash password
  const hashedPassword = await bcrypt.hash(password, 10);

  const handleInsert = async (idToUse) => {
    const insertQuery = `
      INSERT INTO Students (
        StudentID, FullName, Course, YearLevel, Section,
        Email, PhoneNumber, Password,
        EnrollmentStatus, AccountStatus
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'Active', 'Allowed')
    `;

    const [result] = await db.execute(
      insertQuery,
      [idToUse, fullName, course, yearLevel, section, email, phoneNumber, hashedPassword]
    );

    res.status(201).json({
      success: true,
      message: '✅ Student registered successfully',
      data: {
        studentID: idToUse,
        fullName,
        email,
        course,
        yearLevel,
        section
      }
    });
  };

  if (studentID && studentID.trim() !== '') {
    // Check if manual StudentID already exists
    const checkQuery = `SELECT * FROM Students WHERE StudentID = ?`;
    const [existing] = await db.execute(checkQuery, [studentID]);

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        error: '❌ StudentID already exists.'
      });
    }

    await handleInsert(studentID);
  } else {
    // Auto-generate StudentID
    const getLastIDQuery = `SELECT StudentID FROM Students ORDER BY StudentID DESC LIMIT 1`;
    const [results] = await db.execute(getLastIDQuery);

    const currentYear = new Date().getFullYear();
    let newID;

    if (results.length === 0) {
      newID = `${currentYear}-00001`;
    } else {
      const lastID = results[0].StudentID;
      const [year, number] = lastID.split('-');
      const nextNumber = String(parseInt(number) + 1).padStart(5, '0');
      newID = `${year}-${nextNumber}`;
    }

    await handleInsert(newID);
  }
}));

// POST /login - Student login
router.post('/login', validateStudentLogin, asyncHandler(async (req, res) => {
  const { studentId, password } = req.body;

  console.log('🚀 Student login attempt for:', studentId);

  // Find student by StudentID
  const selectQuery = `SELECT * FROM Students WHERE StudentID = ? AND AccountStatus = 'Allowed'`;
  const [results] = await db.execute(selectQuery, [studentId]);

  if (results.length === 0) {
    console.log('❌ Student not found or account not allowed:', studentId);
    return res.status(401).json({
      success: false,
      error: '❌ Invalid student ID or password'
    });
  }

  const student = results[0];
  console.log('🔍 Found student:', student.FullName);

  // Verify password
  const isPasswordValid = await bcrypt.compare(password, student.Password);
  if (!isPasswordValid) {
    console.log('❌ Invalid password for student:', studentId);
    return res.status(401).json({
      success: false,
      error: '❌ Invalid student ID or password'
    });
  }

  // Remove password from response
  delete student.Password;

  // Generate JWT token
  const token = generateStudentToken(student);

  console.log('✅ Student login successful:', student.FullName);

  res.json({
    success: true,
    message: '✅ Student login successful',
    data: student,
    token: token
  });
}));

// POST /validate-session - Validate student session
router.post('/validate-session', asyncHandler(async (req, res) => {
  const { token } = req.body;

  if (!token) {
    return res.status(401).json({
      success: false,
      error: '❌ Token required for session validation'
    });
  }

  try {
    // Verify JWT token
    const decoded = jwt.verify(token, JWT_SECRET);

    // Check if student still exists and is active
    const selectQuery = `SELECT StudentID, FullName, Email, Course, YearLevel, Section, PhoneNumber, EnrollmentStatus, AccountStatus FROM Students WHERE StudentID = ? AND AccountStatus = 'Allowed'`;
    const [results] = await db.execute(selectQuery, [decoded.studentId]);

    if (results.length === 0) {
      return res.status(401).json({
        success: false,
        error: '❌ Student not found or account not allowed'
      });
    }

    const student = results[0];

    res.json({
      success: true,
      message: '✅ Session valid',
      data: student
    });
  } catch (error) {
    console.log('❌ Token validation failed:', error.message);
    return res.status(401).json({
      success: false,
      error: '❌ Invalid or expired token'
    });
  }
}));

// DELETE /delete-student/:studentID
router.delete('/delete-student/:studentID', validateStudentId, asyncHandler(async (req, res) => {
  const { studentID } = req.params;

  const deleteQuery = `DELETE FROM Students WHERE StudentID = ?`;
  const [result] = await db.execute(deleteQuery, [studentID]);

  if (result.affectedRows === 0) {
    return res.status(404).json({
      success: false,
      error: '❌ Student not found.'
    });
  }

  res.json({
    success: true,
    message: `🗑️ Student with ID ${studentID} has been deleted.`
  });
}));

// GET /get-student/:studentID
router.get('/get-student/:studentID', validateStudentId, asyncHandler(async (req, res) => {
  const { studentID } = req.params;

  const selectQuery = `SELECT * FROM Students WHERE StudentID = ?`;
  const [results] = await db.execute(selectQuery, [studentID]);

  if (results.length === 0) {
    return res.status(404).json({
      success: false,
      error: '❌ Student not found.'
    });
  }

  const student = results[0];
  delete student.Password;

  res.json({
    success: true,
    message: '✅ Student found',
    data: student
  });
}));

// GET /get-all-students
router.get('/get-all-students', asyncHandler(async (req, res) => {
  const selectQuery = `SELECT StudentID, FullName, Course, YearLevel, Section, Email, PhoneNumber, EnrollmentStatus, AccountStatus, CreatedAt, UpdatedAt FROM Students ORDER BY StudentID ASC`;

  const [results] = await db.execute(selectQuery);

  res.json({
    success: true,
    message: '✅ Students retrieved successfully',
    count: results.length,
    data: results
  });
}));

// PUT /update-student/:studentID
router.put('/update-student/:studentID', validateStudentUpdate, asyncHandler(async (req, res) => {
  const { studentID } = req.params;
  let {
    fullName,
    course,
    yearLevel,
    section,
    email,
    phoneNumber,
    password,
    enrollmentStatus,
    accountStatus
  } = req.body;

  // Check if student exists
  const checkQuery = `SELECT * FROM Students WHERE StudentID = ?`;
  const [results] = await db.execute(checkQuery, [studentID]);

  if (results.length === 0) {
    return res.status(404).json({
      success: false,
      error: '❌ Student not found.'
    });
  }

  const currentStudent = results[0];

  // Use existing values if not provided
  fullName = fullName || currentStudent.FullName;
  course = course || currentStudent.Course;
  yearLevel = yearLevel !== undefined ? parseInt(yearLevel) : currentStudent.YearLevel;
  section = section || currentStudent.Section;
  email = email || currentStudent.Email;
  phoneNumber = phoneNumber || currentStudent.PhoneNumber;
  enrollmentStatus = enrollmentStatus || currentStudent.EnrollmentStatus;
  accountStatus = accountStatus || currentStudent.AccountStatus;

  let updateQuery = `
    UPDATE Students SET
    FullName = ?, Course = ?, YearLevel = ?, Section = ?,
    Email = ?, PhoneNumber = ?, EnrollmentStatus = ?, AccountStatus = ?
  `;
  let queryParams = [fullName, course, yearLevel, section, email, phoneNumber, enrollmentStatus, accountStatus];

  if (password) {
    const hashedPassword = await bcrypt.hash(password, 10);
    updateQuery += `, Password = ?`;
    queryParams.push(hashedPassword);
  }

  updateQuery += ` WHERE StudentID = ?`;
  queryParams.push(studentID);

  const [updateResult] = await db.execute(updateQuery, queryParams);

  if (updateResult.affectedRows === 0) {
    return res.status(404).json({
      success: false,
      error: '❌ Student not found.'
    });
  }

  res.json({
    success: true,
    message: '✅ Student updated successfully',
    data: {
      studentID,
      fullName,
      email,
      course,
      yearLevel,
      section,
      enrollmentStatus,
      accountStatus
    }
  });
}));

module.exports = router;

/*



PARA POSTING FORMAT - API v1

POST /register-student
http://localhost:3000/api/v1/auth/register-student
{
  "studentID": "2025-00143",
  "fullName": "Nathaniel Inocando",
  "course": "BSIT",
  "yearLevel": 4,
  "section": "A",
  "email": "<EMAIL>",
  "phoneNumber": "***********",
  "password": "HelloNathan123"
}

DELETE /delete-student/:studentID
http://localhost:3000/api/v1/auth/delete-student/2025-00143

GET /get-student/:studentID
http://localhost:3000/api/v1/auth/get-student/2025-00143

GET /get-all-students
http://localhost:3000/api/v1/auth/get-all-students

PUT /update-student/:studentID
http://localhost:3000/api/v1/auth/update-student/2025-00143
{
  "fullName": "Nathaniel Updated",
  "course": "BSCS",
  "yearLevel": 3,
  "section": "B",
  "email": "<EMAIL>",
  "phoneNumber": "***********",
  "password": "NewPassword123",
  "enrollmentStatus": "Active",
  "accountStatus": "Allowed"
}





*/